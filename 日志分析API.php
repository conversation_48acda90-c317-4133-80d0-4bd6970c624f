<?php
/**
 * 日志分析API - 专用于管理员端日志查看和分析
 * 功能：读取system_logs和logs目录下的日志文件，提供筛选、统计、导出等功能
 * 特性：支持多种日志格式解析，提供详细的日志分析功能
 * 作者：网络验证系统
 * 版本：1.0
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 系统配置参数
define('SYSTEM_LOGS_DIR', 'system_logs');
define('LOGS_DIR', 'logs');
define('MAX_LOG_ENTRIES', 10000);  // 单次最大返回日志条数
define('LOG_ENCODING', 'UTF-8');

// 设置HTTP响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理CORS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'CORS预检请求处理成功',
        'service' => '日志分析API',
        'version' => '1.0'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取所有日志文件列表
 */
function 获取日志文件列表() {
    $日志文件 = [];
    
    // 扫描system_logs目录
    if (is_dir(SYSTEM_LOGS_DIR)) {
        $文件列表 = glob(SYSTEM_LOGS_DIR . '/*.txt');
        foreach ($文件列表 as $文件) {
            $日志文件[] = [
                'path' => $文件,
                'name' => basename($文件),
                'size' => filesize($文件),
                'modified' => filemtime($文件),
                'type' => 'system'
            ];
        }
    }
    
    // 扫描logs目录
    if (is_dir(LOGS_DIR)) {
        $文件列表 = glob(LOGS_DIR . '/*.txt');
        foreach ($文件列表 as $文件) {
            $日志文件[] = [
                'path' => $文件,
                'name' => basename($文件),
                'size' => filesize($文件),
                'modified' => filemtime($文件),
                'type' => 'application'
            ];
        }
    }
    
    // 按修改时间倒序排列
    usort($日志文件, function($a, $b) {
        return $b['modified'] - $a['modified'];
    });
    
    return $日志文件;
}

/**
 * 解析日志行
 */
function 解析日志行($行内容) {
    $行内容 = trim($行内容);
    if (empty($行内容)) {
        return null;
    }
    
    // 尝试解析标准格式：[时间] [级别-中文级别] [IP:xxx] 消息 | 附加数据:xxx | UA:xxx
    if (preg_match('/^\[([^\]]+)\]\s*\[([^\]]+)\]\s*\[IP:([^\]]+)\]\s*(.+)$/', $行内容, $matches)) {
        $时间 = $matches[1];
        $级别信息 = $matches[2];
        $IP地址 = $matches[3];
        $剩余内容 = $matches[4];
        
        // 解析级别信息
        $级别 = 'INFO';
        if (preg_match('/^([A-Z]+)-/', $级别信息, $级别匹配)) {
            $级别 = $级别匹配[1];
        }
        
        // 解析消息和附加数据
        $消息内容 = $剩余内容;
        $附加数据 = '';
        $用户代理 = '';
        
        if (strpos($剩余内容, ' | ') !== false) {
            $部分 = explode(' | ', $剩余内容);
            $消息内容 = $部分[0];
            
            foreach ($部分 as $部分内容) {
                if (strpos($部分内容, '附加数据:') === 0) {
                    $附加数据 = substr($部分内容, 5);
                } elseif (strpos($部分内容, 'UA:') === 0) {
                    $用户代理 = substr($部分内容, 3);
                }
            }
        }
        
        return [
            '时间' => $时间,
            '级别' => $级别,
            'IP地址' => $IP地址,
            '消息内容' => $消息内容,
            '附加数据' => $附加数据,
            '用户代理' => $用户代理,
            '原始内容' => $行内容
        ];
    }
    
    // 尝试解析简单格式
    if (preg_match('/^\[([^\]]+)\]\s*\[([^\]]+)\]\s*(.+)$/', $行内容, $matches)) {
        return [
            '时间' => $matches[1],
            '级别' => 'INFO',
            'IP地址' => '未知',
            '消息内容' => $matches[3],
            '附加数据' => '',
            '用户代理' => '',
            '原始内容' => $行内容
        ];
    }
    
    // 无法解析的行，作为普通消息处理
    return [
        '时间' => date('Y-m-d H:i:s'),
        '级别' => 'INFO',
        'IP地址' => '未知',
        '消息内容' => $行内容,
        '附加数据' => '',
        '用户代理' => '',
        '原始内容' => $行内容
    ];
}

/**
 * 读取和解析日志文件
 */
function 读取日志文件($筛选条件 = []) {
    $日志条目 = [];
    $日志文件列表 = 获取日志文件列表();
    
    $级别筛选 = $筛选条件['level'] ?? '';
    $时间范围 = $筛选条件['time_range'] ?? '';
    $IP筛选 = $筛选条件['ip'] ?? '';
    $关键词筛选 = $筛选条件['keyword'] ?? '';
    
    // 计算时间范围
    $开始时间 = null;
    $结束时间 = null;
    
    switch ($时间范围) {
        case 'today':
            $开始时间 = strtotime('today');
            $结束时间 = strtotime('tomorrow') - 1;
            break;
        case 'yesterday':
            $开始时间 = strtotime('yesterday');
            $结束时间 = strtotime('today') - 1;
            break;
        case 'week':
            $开始时间 = strtotime('-7 days');
            $结束时间 = time();
            break;
        case 'month':
            $开始时间 = strtotime('-30 days');
            $结束时间 = time();
            break;
    }
    
    foreach ($日志文件列表 as $文件信息) {
        if (!file_exists($文件信息['path'])) {
            continue;
        }
        
        $文件句柄 = fopen($文件信息['path'], 'r');
        if (!$文件句柄) {
            continue;
        }
        
        while (($行 = fgets($文件句柄)) !== false) {
            $日志项 = 解析日志行($行);
            if (!$日志项) {
                continue;
            }
            
            // 应用筛选条件
            $通过筛选 = true;
            
            // 级别筛选
            if (!empty($级别筛选) && $日志项['级别'] !== $级别筛选) {
                $通过筛选 = false;
            }
            
            // IP筛选
            if (!empty($IP筛选) && strpos($日志项['IP地址'], $IP筛选) === false) {
                $通过筛选 = false;
            }
            
            // 关键词筛选
            if (!empty($关键词筛选)) {
                $搜索内容 = $日志项['消息内容'] . ' ' . $日志项['附加数据'] . ' ' . $日志项['用户代理'];
                if (stripos($搜索内容, $关键词筛选) === false) {
                    $通过筛选 = false;
                }
            }
            
            // 时间范围筛选
            if ($开始时间 && $结束时间) {
                $日志时间 = strtotime($日志项['时间']);
                if ($日志时间 < $开始时间 || $日志时间 > $结束时间) {
                    $通过筛选 = false;
                }
            }
            
            if ($通过筛选) {
                $日志项['文件'] = $文件信息['name'];
                $日志项['文件类型'] = $文件信息['type'];
                $日志条目[] = $日志项;
                
                // 限制返回数量
                if (count($日志条目) >= MAX_LOG_ENTRIES) {
                    break 2;
                }
            }
        }
        
        fclose($文件句柄);
    }
    
    // 按时间倒序排列
    usort($日志条目, function($a, $b) {
        return strtotime($b['时间']) - strtotime($a['时间']);
    });
    
    return $日志条目;
}

/**
 * 获取日志统计信息
 */
function 获取日志统计() {
    $日志条目 = 读取日志文件();
    
    $统计信息 = [
        '总日志条数' => count($日志条目),
        '错误日志' => 0,
        '警告日志' => 0,
        '今日日志' => 0,
        '级别统计' => [],
        'IP统计' => []
    ];
    
    $今日开始 = strtotime('today');
    
    foreach ($日志条目 as $日志项) {
        // 级别统计
        $级别 = $日志项['级别'];
        if (!isset($统计信息['级别统计'][$级别])) {
            $统计信息['级别统计'][$级别] = 0;
        }
        $统计信息['级别统计'][$级别]++;
        
        // 错误和警告统计
        if ($级别 === 'ERROR' || $级别 === 'CRITICAL') {
            $统计信息['错误日志']++;
        } elseif ($级别 === 'WARNING') {
            $统计信息['警告日志']++;
        }
        
        // 今日日志统计
        $日志时间 = strtotime($日志项['时间']);
        if ($日志时间 >= $今日开始) {
            $统计信息['今日日志']++;
        }
        
        // IP统计（只统计前100个）
        $IP = $日志项['IP地址'];
        if ($IP !== '未知' && count($统计信息['IP统计']) < 100) {
            if (!isset($统计信息['IP统计'][$IP])) {
                $统计信息['IP统计'][$IP] = 0;
            }
            $统计信息['IP统计'][$IP]++;
        }
    }
    
    // 按访问次数排序IP统计
    arsort($统计信息['IP统计']);
    
    return $统计信息;
}

// 处理请求
try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get_logs':
            // 获取日志列表
            $筛选条件 = [
                'level' => $_GET['level'] ?? '',
                'time_range' => $_GET['time_range'] ?? '',
                'ip' => $_GET['ip'] ?? '',
                'keyword' => $_GET['keyword'] ?? ''
            ];
            
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = max(1, min(1000, intval($_GET['limit'] ?? 100)));
            
            $日志条目 = 读取日志文件($筛选条件);
            $总数 = count($日志条目);
            
            // 分页处理
            $开始索引 = ($page - 1) * $limit;
            $分页日志 = array_slice($日志条目, $开始索引, $limit);
            
            echo json_encode([
                'status' => 'success',
                'data' => $分页日志,
                'total' => $总数,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($总数 / $limit)
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_log_stats':
            // 获取日志统计
            $统计信息 = 获取日志统计();
            
            echo json_encode([
                'status' => 'success',
                'data' => $统计信息
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'export_logs':
            // 导出日志
            $筛选条件 = [
                'level' => $_GET['level'] ?? '',
                'time_range' => $_GET['time_range'] ?? '',
                'ip' => $_GET['ip'] ?? '',
                'keyword' => $_GET['keyword'] ?? ''
            ];
            
            $日志条目 = 读取日志文件($筛选条件);
            
            header('Content-Type: text/plain; charset=utf-8');
            header('Content-Disposition: attachment; filename="logs_' . date('Y-m-d_H-i-s') . '.txt"');
            
            echo "# 日志导出文件\n";
            echo "# 导出时间: " . date('Y-m-d H:i:s') . "\n";
            echo "# 总条数: " . count($日志条目) . "\n";
            echo "# 筛选条件: " . json_encode($筛选条件, JSON_UNESCAPED_UNICODE) . "\n";
            echo str_repeat("=", 80) . "\n\n";
            
            foreach ($日志条目 as $日志项) {
                echo $日志项['原始内容'] . "\n";
            }
            break;
            
        case 'cleanup_logs':
            // 清理旧日志
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            $days = max(1, intval($data['days'] ?? 30));
            
            $清理时间 = time() - ($days * 24 * 60 * 60);
            $清理数量 = 0;
            
            $日志文件列表 = 获取日志文件列表();
            foreach ($日志文件列表 as $文件信息) {
                if ($文件信息['modified'] < $清理时间) {
                    if (unlink($文件信息['path'])) {
                        $清理数量++;
                    }
                }
            }
            
            echo json_encode([
                'status' => 'success',
                'message' => "成功清理了 {$清理数量} 个旧日志文件",
                'cleaned_count' => $清理数量
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'status' => 'error',
                'message' => '不支持的操作类型'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
